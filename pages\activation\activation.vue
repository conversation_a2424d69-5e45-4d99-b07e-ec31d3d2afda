<template>
	<view class="container">
		<!-- 页面头部 -->
		<view class="header">
			<view class="header-title">激活码管理</view>
			<view class="header-subtitle">获取激活码与额度查询</view>
		</view>

		<!-- 激活码管理卡片 -->
		<view class="card fade-in">
			<view class="card-header">
				<view class="card-title">我的激活码</view>
			</view>
			<view class="card-content">
				<!-- 未登录状态 -->
				<view v-if="!userInfo" class="token-input-section">
					<view class="input-group">
						<input v-model="inputToken" placeholder="请输入激活码" class="token-input" maxlength="50" />
						<button class="btn btn-primary" @click="verifyToken">验证激活码</button>
					</view>

					<!-- 获取激活码 -->
					<view v-if="!addTokenFlag" class="get-token-section">
						<view class="divider">或</view>
						<input v-model="newToken" placeholder="点击按钮获取激活码" class="token-input" disabled />
						<button class="btn btn-success" @click="addToken">获取激活码</button>
					</view>
				</view>

				<!-- 已登录状态 -->
				<view v-else class="token-info-section">
					<!-- 警告提示 -->
					<view class="alert alert-warning">
						<view class="alert-icon">⚠️</view>
						<view class="alert-text">请妥善保管您的激活码，请勿泄露给他人</view>
					</view>

					<!-- 激活码信息 -->
					<view class="info-list">
						<view class="info-item">
							<view class="info-label">激活码:</view>
							<view class="info-value-group">
								<view class="info-value">{{ userInfo.tokenName }}</view>
								<view class="copy-btn" @click="copyText(userInfo.tokenName)">📋</view>
							</view>
						</view>
						<view class="info-item">
							<view class="info-label">绑定设备:</view>
							<view class="info-value">{{ userInfo.tokenCompCode || '未绑定设备' }}</view>
						</view>
						<view class="info-item">
							<view class="info-label">创建时间:</view>
							<view class="info-value">{{ userInfo.tokenTime }}</view>
						</view>
						<view class="info-item">
							<view class="info-label">账户状态:</view>
							<view class="tag" :class="userInfo.tokenStatus === 0 ? 'tag-success' : 'tag-danger'">
								{{ userInfo.tokenStatus === 0 ? '正常' : '已禁用' }}
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 额度管理卡片 -->
		<view v-if="userInfo" class="card fade-in" style="animation-delay: 0.1s;">
			<view class="card-header">
				<view class="card-title">额度使用情况</view>
			</view>
			<view class="card-content">
				<!-- 主要额度显示 -->
				<view class="quota-main">
					<view class="quota-card today-usage">
						<view class="quota-icon">📅</view>
						<view class="quota-content">
							<view class="quota-value">{{ todayUsage || 0 }}</view>
							<view class="quota-label">今日使用额度</view>
						</view>
					</view>
					<view class="quota-card remaining-total">
						<view class="quota-icon">💰</view>
						<view class="quota-content">
							<view class="quota-value">{{ remainingTotalQuota || 0 }}</view>
							<view class="quota-label">剩余总额度</view>
							<view class="quota-sublabel">基础+签到+活动+永久</view>
						</view>
					</view>
				</view>

				<!-- 详细额度信息 -->
				<view class="quota-details">
					<view class="quota-detail-item">
						<view class="quota-detail-value">{{ userQuota.baseLimit || 0 }}</view>
						<view class="quota-detail-label">基础额度</view>
					</view>
					<view class="quota-detail-item">
						<view class="quota-detail-value">{{ userQuota.activityLimit || 0 }}</view>
						<view class="quota-detail-label">活动额度</view>
					</view>
					<view class="quota-detail-item">
						<view class="quota-detail-value">{{ userQuota.tempLimit || 0 }}</view>
						<view class="quota-detail-label">签到额度</view>
					</view>
					<view class="quota-detail-item">
						<view class="quota-detail-value">{{ userQuota.permanentQuota || 0 }}</view>
						<view class="quota-detail-label">永久额度</view>
					</view>
				</view>

				<!-- 永久额度提示 -->
				<view class="alert alert-warning">
					<view class="alert-icon">⚠️</view>
					<view class="alert-text">永久额度是一次性消耗品，不会刷新重置！永久额度只有在其他额度用完后才会使用！</view>
				</view>

				<!-- 操作按钮 -->
				<view class="action-buttons">
					<button class="btn btn-success" @click="refreshQuota">刷新额度</button>
					<button class="btn btn-danger" @click="logout">重置激活码</button>
				</view>
			</view>
		</view>

		<!-- 导航按钮 -->
		<view class="nav-buttons">
			<button class="btn btn-primary nav-btn" @click="goToSignIn">前往签到</button>
			<button class="btn btn-secondary nav-btn" @click="goBack">返回首页</button>
		</view>

		<!-- 激活码获取成功弹窗 -->
		<view v-if="showTokenDialog" class="modal-overlay" @click="handleTokenDialogClose">
			<view class="modal-content" @click.stop="">
				<view class="modal-header">
					<view class="modal-title">激活码获取成功</view>
					<view class="modal-close" @click="handleTokenDialogClose">×</view>
				</view>
				<view class="token-dialog-content">
					<view class="token-dialog-tip">
						<text>⚠️ 请妥善保管您的激活码，请勿泄露给他人</text>
					</view>
					<view class="token-dialog-code">
						<view class="token-dialog-label">您的激活码:</view>
						<view class="token-dialog-value-group">
							<view class="token-dialog-value">{{ newToken }}</view>
							<view class="copy-btn" @click="copyText(newToken)">📋</view>
						</view>
					</view>

					<view class="token-dialog-steps">
						<view class="step-item completed">
							<view class="step-number">1</view>
							<view class="step-text">已获取激活码</view>
							<view class="step-status">✅</view>
						</view>
						<view class="step-item">
							<view class="step-number">2</view>
							<view class="step-text">去小说下载器中激活该激活码</view>
							<view class="step-status">➡️</view>
						</view>
						<!-- <view class="step-item">
							<view class="step-number">3</view>
							<view class="step-text">回到此页面，验证激活码</view>
							<view class="step-status">➡️</view>
						</view> -->
					</view>
				</view>
				<view class="modal-footer">
					<button class="btn btn-primary" @click="handleTokenDialogClose">确定</button>
				</view>
			</view>
		</view>

		<!-- 赞助弹窗 -->
		<view v-if="showDonateDialog" class="modal-overlay" @click="closeDonateDialog">
			<view class="modal-content" @click.stop="">
				<view class="modal-header">
					<view class="modal-title">支持我们的开发</view>
					<view class="modal-close" @click="closeDonateDialog">×</view>
				</view>
				<view class="donate-dialog-content">
					<view class="donate-header">
						<view class="donate-title">感谢您的支持</view>
						<view class="donate-description">
							您的支持是我们持续改进的动力。如果您觉得我们的工具对您有所帮助，可以考虑给予我们一些赞助。
						</view>
					</view>

					<view class="donate-methods">
						<view class="donate-method">
							<view class="method-title">支付宝</view>
							<view class="qr-placeholder">支付宝收款码</view>
						</view>
						<view class="donate-method">
							<view class="method-title">微信支付</view>
							<view class="qr-placeholder">微信收款码</view>
						</view>
					</view>

					<view class="donate-footer">
						<view class="donate-tip">赞助为自愿行为，不影响软件的正常使用</view>
						<view class="donate-tip">赞助后请留下您的联系方式或者激活码，我们将回馈您一定额度支持</view>
					</view>
				</view>
				<view class="modal-footer">
					<button class="btn btn-success" @click="closeDonateDialog">关闭</button>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		tokenApi,
		utils
	} from '@/api/index'
	import {
		checkLogin
	} from '@/utils/auth'

	import CryptoJS from "@/utils/crypto-js.min.js"

	export default {
		data() {
			return {
				// 用户信息
				userInfo: null,
				inputToken: '',
				userQuota: {
					baseLimit: 0,
					activityLimit: 0,
					tempLimit: 0,
					totalLimit: 0,
					currentUseNum: 0,
					remainingLimit: 0,
					permanentQuota: 0
				},

				// 激活码相关
				newToken: '',
				addTokenFlag: true,
				tokenName: '',

				// 弹窗状态
				showTokenDialog: false,
				showDonateDialog: false,
				counter: 0,

				// 隐私授权相关
				resolvePrivacyAuthorization: null
			}
		},

		computed: {
			todayUsage() {
				return this.userQuota.currentUseNum || 0
			},
			remainingTotalQuota() {
				const totalQuota = (this.userQuota.totalLimit || 0) + (this.userQuota.permanentQuota || 0)
				const used = this.userQuota.currentUseNum || 0
				return Math.max(0, totalQuota - used)
			}
		},

		onLoad() {
			// 检查登录状态
			if (!checkLogin()) {
				return
			}

			// #ifdef MP-WEIXIN
			// 设置隐私授权监听器
			this.setupPrivacyListener()
			// #endif

			this.initPage()
		},

		methods: {
			// 初始化页面
			async initPage() {
				try {
					// 获取用户token状态
					const tokenRes = await tokenApi.getUserToken()
					if (tokenRes.code === 200 && tokenRes.data) {
						this.addTokenFlag = true
						this.tokenName = tokenRes.data.tokenName
						if (this.tokenName) {
							this.inputToken = this.tokenName
							await this.verifyToken()
						}
					} else {
						this.addTokenFlag = false
					}
				} catch (error) {
					console.error('获取用户token失败:', error)
					this.addTokenFlag = false
				}
			},

			// 验证激活码
			async verifyToken() {
				if (!this.inputToken) {
					uni.showToast({
						title: '请输入激活码',
						icon: 'none',
						duration: 1500
					})
					return
				}

				try {
					// 获取激活码信息
					const res = await tokenApi.getQuota(this.inputToken)
					if (res.code === 200) {
						// 保存激活码到本地存储
						uni.setStorageSync('fq_token', this.inputToken)

						// 设置用户信息
						this.userInfo = {
							tokenName: this.inputToken,
							tokenTime: utils.formatDate(new Date(), 'YYYY-MM-DD'),
							tokenStatus: 0,
							tokenCompCode: '尚未绑定'
						}

						// 保存额度信息
						this.userQuota = res.data || {
							baseLimit: 0,
							activityLimit: 0,
							tempLimit: 0,
							totalLimit: 0,
							currentUseNum: 0,
							remainingLimit: 0,
							permanentQuota: 0
						}

						// 尝试获取额外的token信息
						try {
							const detailRes = await tokenApi.getTokenInfo(this.inputToken)
							if (detailRes.code === 200 && detailRes.rows && detailRes.rows.length > 0) {
								const tokenDetail = detailRes.rows[0]
								this.userInfo = {
									...this.userInfo,
									...tokenDetail
								}
							}
						} catch (error) {
							console.error('获取token详情失败:', error)
						}

						uni.showToast({
							title: '激活码验证成功',
							icon: 'success',
							duration: 1500
						})
					} else {
						uni.showToast({
							title: res.msg || '激活码无效',
							icon: 'none',
							duration: 2000
						})
					}
				} catch (error) {
					console.error('验证激活码失败:', error)
					uni.showToast({
						title: '验证激活码失败，请稍后再试',
						icon: 'none',
						duration: 2000
					})
				}
			},

			// 获取激活码
			async addToken() {
				try {
					const time = utils.getCurrentTimeString()
					const nowTime = new Date().getTime()
					// 简化的激活码生成，实际项目中建议使用更安全的方法
					// const tokenName = utils.encrypt('huswhusbg', 'wqowwjnsm', time.toString())
					console.log("试卷===", this.generateUniqueId(nowTime.toString()));
					const tokenName = this.generateUniqueId(nowTime.toString())


					const res = await tokenApi.addToken(tokenName, time)
					if (res.code === 200) {
						this.newToken = tokenName
						this.inputToken = tokenName

						// 显示弹窗
						this.showTokenDialog = true

						// 存储激活码
						uni.setStorageSync('fq_token', this.inputToken)
					} else {
						uni.showToast({
							title: res.msg || '获取激活码失败',
							icon: 'none',
							duration: 2000
						})
					}
				} catch (error) {
					console.error('获取激活码失败:', error)
					uni.showToast({
						title: '获取激活码失败，请稍后再试',
						icon: 'none',
						duration: 2000
					})
				}
			},

			// 生成唯一ID的函数
			generateUniqueId(timestamp = null) {
				// 使用当前时间戳（毫秒），如果未提供
				const now = timestamp ? new Date(timestamp) : new Date();
				const timePart = now.getTime().toString();

				// 增加计数器并重置（如果计数器太大）
				this.counter = (this.counter + 1) % 10000;

				// 生成随机数部分
				const randomPart = Math.floor(Math.random() * 1000000).toString();

				// 组合所有部分
				const combined = timePart + this.counter.toString().padStart(4, '0') + randomPart;

				// 使用SHA256生成哈希（在UniApp中可以使用uni.getSystemInfoSync()等设备信息增强唯一性）
				const hash = CryptoJS.MD5(combined).toString();

				return hash;
			},

			// 激活码弹窗关闭处理
			async handleTokenDialogClose() {
				this.showTokenDialog = false
				this.addTokenFlag = true
				await this.verifyToken()
			},

			// 刷新额度
			async refreshQuota() {
				if (!this.userInfo) return

				try {
					const res = await tokenApi.refreshQuota(this.userInfo.tokenName)
					if (res.code === 200) {
						this.userQuota = res.data
						uni.showToast({
							title: '额度刷新成功',
							icon: 'success',
							duration: 1500
						})
					}
				} catch (error) {
					console.error('刷新额度失败:', error)
					uni.showToast({
						title: '刷新额度失败',
						icon: 'none',
						duration: 1500
					})
				}
			},

			// 重置激活码
			logout() {
				uni.showModal({
					title: '提示',
					content: '确定要重置激活码吗？重置激活码不会重置额度，该按钮旨在帮助遇见bug用户清空当前的激活码，一般情况不要点！',
					success: (res) => {
						if (res.confirm) {
							uni.removeStorageSync('fq_token')
							this.userInfo = null
							this.userQuota = {
								baseLimit: 0,
								activityLimit: 0,
								tempLimit: 0,
								totalLimit: 0,
								currentUseNum: 0,
								remainingLimit: 0,
								permanentQuota: 0
							}
							this.inputToken = ''
							this.addTokenFlag = false

							uni.showToast({
								title: '已重置激活码',
								icon: 'success',
								duration: 1500
							})
						}
					}
				})
			},

			// 复制文本
			async copyText(text) {
				try {
					await utils.copyText(text)
				} catch (error) {
					console.error('复制失败:', error)
					// 如果是隐私授权相关错误，提供更友好的提示
					if (error.errMsg && error.errMsg.includes('api scope is not declared')) {
						uni.showModal({
							title: '温馨提示',
							content: '复制失败',
							showCancel: false,
							confirmText: '我知道了'
						})
					}
				}
			},

			// #ifdef MP-WEIXIN
			// 设置隐私监听器
			setupPrivacyListener() {
				if (typeof wx !== 'undefined' && wx.onNeedPrivacyAuthorization) {
					wx.onNeedPrivacyAuthorization((resolve, eventInfo) => {
						console.log('触发隐私授权事件，接口：', eventInfo.referrer)
						// 需要用户同意隐私授权时的处理
						this.resolvePrivacyAuthorization = resolve

						// 显示授权提示
						uni.showModal({
							title: '隐私授权',
							content: '应用需要使用剪贴板功能来复制激活码，请您授权',
							confirmText: '同意',
							cancelText: '拒绝',
							success: (res) => {
								if (res.confirm) {
									// 用户同意授权
									if (this.resolvePrivacyAuthorization) {
										this.resolvePrivacyAuthorization({
											buttonId: 'agree-privacy-btn',
											event: 'agree'
										})
										this.resolvePrivacyAuthorization = null
									}
								} else {
									// 用户拒绝授权
									if (this.resolvePrivacyAuthorization) {
										this.resolvePrivacyAuthorization({
											event: 'disagree'
										})
										this.resolvePrivacyAuthorization = null
									}
								}
							}
						})
					})
				}
			},
			// #endif

			// 打开赞助弹窗
			openDonateDialog() {
				this.showDonateDialog = true
			},

			// 关闭赞助弹窗
			closeDonateDialog() {
				this.showDonateDialog = false
			},

			// 前往签到页面
			goToSignIn() {
				uni.navigateTo({
					url: '/pages/signin/signin'
				})
			},

			// 返回首页
			goBack() {
				uni.navigateBack()
			}
		}
	}
</script>

<style scoped>
	/* 页面容器 */
	.container {
		min-height: 100vh;
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		padding: 20rpx;
	}

	/* 页面头部 */
	.header {
		text-align: center;
		padding: 40rpx 0;
		color: #ffffff;
	}

	.header-title {
		font-size: 48rpx;
		font-weight: bold;
		margin-bottom: 10rpx;
		text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
	}

	.header-subtitle {
		font-size: 28rpx;
		opacity: 0.9;
	}

	/* 卡片样式 */
	.card {
		background-color: #ffffff;
		border-radius: 16rpx;
		box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
		margin-bottom: 30rpx;
		overflow: hidden;
		transition: all 0.3s ease;
	}

	.card:hover {
		transform: translateY(-4rpx);
		box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.15);
	}

	.fade-in {
		animation: fadeInUp 0.6s ease-out;
	}

	@keyframes fadeInUp {
		from {
			opacity: 0;
			transform: translateY(30rpx);
		}

		to {
			opacity: 1;
			transform: translateY(0);
		}
	}

	.card-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 30rpx;
		border-bottom: 1rpx solid #f0f0f0;
		background: linear-gradient(90deg, #f8f9fa 0%, #ffffff 100%);
	}

	.card-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333333;
	}

	.card-content {
		padding: 30rpx;
	}

	/* 标签样式 */
	.tag {
		padding: 8rpx 16rpx;
		border-radius: 20rpx;
		font-size: 24rpx;
		font-weight: bold;
	}

	.tag-success {
		background-color: #f0f9eb;
		color: #67C23A;
		border: 1rpx solid #c2e7b0;
	}

	.tag-danger {
		background-color: #fef0f0;
		color: #F56C6C;
		border: 1rpx solid #fbc4c4;
	}

	.tag-info {
		background-color: #f4f4f5;
		color: #909399;
		border: 1rpx solid #d3d4d6;
	}

	/* 按钮样式 */
	.btn {
		padding: 20rpx 40rpx;
		border-radius: 25rpx;
		border: none;
		font-size: 28rpx;
		font-weight: bold;
		cursor: pointer;
		transition: all 0.3s ease;
		text-align: center;
		display: flex;
		align-items: center;
		justify-content: center;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
	}

	.btn:hover {
		transform: translateY(-2rpx);
		box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.15);
	}

	.btn-primary {
		background: linear-gradient(135deg, #409EFF 0%, #66b1ff 100%);
		color: #ffffff;
	}

	.btn-success {
		background: linear-gradient(135deg, #67C23A 0%, #85ce61 100%);
		color: #ffffff;
	}

	.btn-danger {
		background: linear-gradient(135deg, #F56C6C 0%, #f78989 100%);
		color: #ffffff;
	}

	.btn-warning {
		background: linear-gradient(135deg, #E6A23C 0%, #ebb563 100%);
		color: #ffffff;
	}

	.btn-secondary {
		background: linear-gradient(135deg, #909399 0%, #a6a9ad 100%);
		color: #ffffff;
	}

	.btn-disabled {
		background-color: #c0c4cc;
		color: #ffffff;
		cursor: not-allowed;
		opacity: 0.6;
	}

	.btn-disabled:hover {
		transform: none;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
	}

	/* 激活码输入区域 */
	.token-input-section {
		padding: 20rpx 0;
	}

	.input-group {
		display: flex;
		flex-direction: column;
		gap: 20rpx;
		margin-bottom: 30rpx;
	}

	.token-input {
		height: 80rpx;
		padding: 0 20rpx;
		border: 2rpx solid #e0e0e0;
		border-radius: 40rpx;
		font-size: 28rpx;
		background-color: #ffffff;
		transition: all 0.3s ease;
	}

	.token-input:focus {
		border-color: #409EFF;
		box-shadow: 0 0 0 4rpx rgba(64, 158, 255, 0.1);
	}

	.get-token-section {
		text-align: center;
	}

	.divider {
		margin: 30rpx 0;
		font-size: 24rpx;
		color: #999999;
		position: relative;
	}

	.divider::before,
	.divider::after {
		content: '';
		position: absolute;
		top: 50%;
		width: 30%;
		height: 1rpx;
		background-color: #e0e0e0;
	}

	.divider::before {
		left: 0;
	}

	.divider::after {
		right: 0;
	}

	/* 激活码信息区域 */
	.token-info-section {
		padding: 20rpx 0;
	}

	.alert {
		display: flex;
		align-items: center;
		padding: 20rpx;
		border-radius: 8rpx;
		margin-bottom: 30rpx;
	}

	.alert-warning {
		background-color: #fdf6ec;
		border: 1rpx solid #f5dab1;
	}

	.alert-icon {
		font-size: 32rpx;
		margin-right: 15rpx;
	}

	.alert-text {
		flex: 1;
		font-size: 26rpx;
		color: #E6A23C;
	}

	.info-list {
		margin-bottom: 30rpx;
	}

	.info-item {
		display: flex;
		align-items: center;
		padding: 20rpx 0;
		border-bottom: 1rpx solid #f0f0f0;
	}

	.info-item:last-child {
		border-bottom: none;
	}

	.info-label {
		width: 150rpx;
		font-size: 28rpx;
		color: #666666;
	}

	.info-value {
		flex: 1;
		font-size: 28rpx;
		font-weight: bold;
		color: #333333;
	}

	.info-value-group {
		flex: 1;
		display: flex;
		align-items: center;
		justify-content: space-between;
	}

	.copy-btn {
		padding: 10rpx;
		font-size: 32rpx;
		color: #409EFF;
		cursor: pointer;
	}

	/* 额度管理样式 */
	.quota-main {
		display: flex;
		gap: 20rpx;
		margin-bottom: 30rpx;
	}

	.quota-card {
		flex: 1;
		display: flex;
		align-items: center;
		padding: 30rpx 20rpx;
		border-radius: 16rpx;
		box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.1);
		transition: all 0.3s ease;
	}

	.quota-card:hover {
		transform: translateY(-4rpx);
		box-shadow: 0 8rpx 25rpx rgba(0, 0, 0, 0.15);
	}

	.today-usage {
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		color: #ffffff;
	}

	.remaining-total {
		background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
		color: #ffffff;
	}

	.quota-icon {
		font-size: 48rpx;
		margin-right: 20rpx;
		opacity: 0.8;
	}

	.quota-content {
		flex: 1;
	}

	.quota-value {
		font-size: 42rpx;
		font-weight: bold;
		margin-bottom: 8rpx;
		text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
	}

	.quota-label {
		font-size: 24rpx;
		opacity: 0.9;
	}

	.quota-sublabel {
		font-size: 20rpx;
		opacity: 0.7;
		margin-top: 4rpx;
	}

	/* 详细额度信息 */
	.quota-details {
		display: grid;
		grid-template-columns: repeat(2, 1fr);
		gap: 20rpx;
		margin-bottom: 30rpx;
	}

	.quota-detail-item {
		text-align: center;
		padding: 25rpx 15rpx;
		background-color: #f8f9fa;
		border-radius: 12rpx;
		border: 1rpx solid #e9ecef;
		transition: all 0.3s ease;
	}

	.quota-detail-item:hover {
		background-color: #e3f2fd;
		border-color: #409EFF;
		transform: translateY(-2rpx);
	}

	.quota-detail-value {
		font-size: 36rpx;
		font-weight: bold;
		color: #409EFF;
		margin-bottom: 10rpx;
	}

	.quota-detail-label {
		font-size: 24rpx;
		color: #666666;
	}

	/* 操作按钮 */
	.action-buttons {
		display: flex;
		justify-content: space-around;
		gap: 15rpx;
		margin-top: 30rpx;
	}

	.action-buttons .btn {
		flex: 1;
		height: 70rpx;
		border-radius: 35rpx;
		font-size: 26rpx;
	}

	/* 导航按钮 */
	.nav-buttons {
		display: flex;
		gap: 20rpx;
		margin-top: 30rpx;
	}

	.nav-btn {
		flex: 1;
		height: 80rpx;
		border-radius: 40rpx;
		font-size: 30rpx;
	}

	/* 弹窗样式 */
	.modal-overlay {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(0, 0, 0, 0.5);
		display: flex;
		justify-content: center;
		align-items: center;
		z-index: 1000;
	}

	.modal-content {
		background-color: #ffffff;
		border-radius: 16rpx;
		width: 90%;
		max-width: 600rpx;
		max-height: 80vh;
		overflow-y: auto;
		box-shadow: 0 10rpx 40rpx rgba(0, 0, 0, 0.3);
		animation: modalSlideIn 0.3s ease-out;
	}

	@keyframes modalSlideIn {
		from {
			opacity: 0;
			transform: translateY(-50rpx) scale(0.9);
		}

		to {
			opacity: 1;
			transform: translateY(0) scale(1);
		}
	}

	.modal-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 30rpx;
		border-bottom: 1rpx solid #f0f0f0;
	}

	.modal-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333333;
	}

	.modal-close {
		font-size: 48rpx;
		color: #999999;
		cursor: pointer;
		width: 60rpx;
		height: 60rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		border-radius: 50%;
		transition: all 0.3s ease;
	}

	.modal-close:hover {
		background-color: #f5f5f5;
		color: #666666;
	}

	.modal-footer {
		padding: 30rpx;
		border-top: 1rpx solid #f0f0f0;
		display: flex;
		justify-content: center;
		gap: 20rpx;
	}

	/* 激活码弹窗样式 */
	.token-dialog-content {
		padding: 30rpx;
	}

	.token-dialog-tip {
		background-color: #fdf6ec;
		color: #E6A23C;
		padding: 20rpx;
		border-radius: 8rpx;
		margin-bottom: 30rpx;
		text-align: center;
		font-size: 26rpx;
		border-left: 6rpx solid #E6A23C;
	}

	.token-dialog-code {
		display: flex;
		align-items: center;
		justify-content: center;
		margin-bottom: 30rpx;
		background-color: #f0f9eb;
		padding: 20rpx;
		border-radius: 8rpx;
		border-left: 6rpx solid #67C23A;
	}

	.token-dialog-label {
		font-size: 28rpx;
		color: #333333;
		margin-right: 15rpx;
	}

	.token-dialog-value-group {
		display: flex;
		align-items: center;
		gap: 15rpx;
	}

	.token-dialog-value {
		font-size: 26rpx;
		font-weight: bold;
		color: #67C23A;
		word-break: break-all;
	}

	.token-dialog-steps {
		margin-top: 30rpx;
		text-align: left;
		background-color: #f5f7fa;
		padding: 30rpx;
		border-radius: 12rpx;
	}

	.step-item {
		display: flex;
		align-items: center;
		padding: 20rpx 0;
		border-bottom: 1rpx solid #e0e0e0;
	}

	.step-item:last-child {
		border-bottom: none;
	}

	.step-item.completed {
		color: #67C23A;
	}

	.step-number {
		width: 50rpx;
		height: 50rpx;
		background-color: #409EFF;
		color: #ffffff;
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		font-weight: bold;
		margin-right: 20rpx;
		font-size: 24rpx;
	}

	.step-item.completed .step-number {
		background-color: #67C23A;
	}

	.step-text {
		flex: 1;
		font-size: 26rpx;
	}

	.step-status {
		font-size: 32rpx;
		margin-left: 20rpx;
	}

	/* 赞助弹窗样式 */
	.donate-dialog-content {
		padding: 30rpx 20rpx;
	}

	.donate-header {
		text-align: center;
		margin-bottom: 30rpx;
	}

	.donate-title {
		font-size: 36rpx;
		font-weight: bold;
		color: #333333;
		margin-bottom: 20rpx;
	}

	.donate-description {
		font-size: 28rpx;
		color: #666666;
		line-height: 1.6;
		background-color: #f8f8f8;
		padding: 20rpx;
		border-radius: 8rpx;
	}

	.donate-methods {
		display: flex;
		justify-content: space-around;
		margin: 30rpx 0;
		gap: 30rpx;
	}

	.donate-method {
		flex: 1;
		text-align: center;
	}

	.method-title {
		font-size: 28rpx;
		font-weight: bold;
		color: #333333;
		margin-bottom: 20rpx;
	}

	.qr-placeholder {
		width: 200rpx;
		height: 200rpx;
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		color: #ffffff;
		border-radius: 12rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 24rpx;
		margin: 0 auto;
		box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.1);
	}

	.donate-footer {
		border-top: 1rpx solid #e0e0e0;
		padding-top: 20rpx;
		text-align: center;
	}

	.donate-tip {
		font-size: 24rpx;
		color: #999999;
		margin: 10rpx 0;
		line-height: 1.5;
	}

	/* 响应式设计 */
	@media screen and (max-width: 750rpx) {
		.quota-main {
			flex-direction: column;
		}

		.quota-details {
			grid-template-columns: repeat(2, 1fr);
		}

		.action-buttons {
			flex-direction: column;
		}

		.donate-methods {
			flex-direction: column;
			align-items: center;
		}
	}

	@media screen and (max-width: 600rpx) {
		.quota-details {
			grid-template-columns: 1fr;
		}
	}
</style>