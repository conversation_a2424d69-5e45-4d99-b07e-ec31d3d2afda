






























































































































































































































































































































































































































































































































































































/* 页面容器 */
.container.data-v-4f5b6586 {
	min-height: 100vh;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding: 20rpx;
}
/* 页面头部 */
.header.data-v-4f5b6586 {
	text-align: center;
	padding: 40rpx 0;
	color: #ffffff;
}
.header-title.data-v-4f5b6586 {
	font-size: 48rpx;
	font-weight: bold;
	margin-bottom: 10rpx;
	text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}
.header-subtitle.data-v-4f5b6586 {
	font-size: 28rpx;
	opacity: 0.9;
}
/* 卡片样式 */
.card.data-v-4f5b6586 {
	background-color: #ffffff;
	border-radius: 16rpx;
	box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
	margin-bottom: 30rpx;
	overflow: hidden;
	transition: all 0.3s ease;
}
.card.data-v-4f5b6586:hover {
	-webkit-transform: translateY(-4rpx);
	        transform: translateY(-4rpx);
	box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.15);
}
.fade-in.data-v-4f5b6586 {
	-webkit-animation: fadeInUp-data-v-4f5b6586 0.6s ease-out;
	        animation: fadeInUp-data-v-4f5b6586 0.6s ease-out;
}
@-webkit-keyframes fadeInUp-data-v-4f5b6586 {
from {
		opacity: 0;
		-webkit-transform: translateY(30rpx);
		        transform: translateY(30rpx);
}
to {
		opacity: 1;
		-webkit-transform: translateY(0);
		        transform: translateY(0);
}
}
@keyframes fadeInUp-data-v-4f5b6586 {
from {
		opacity: 0;
		-webkit-transform: translateY(30rpx);
		        transform: translateY(30rpx);
}
to {
		opacity: 1;
		-webkit-transform: translateY(0);
		        transform: translateY(0);
}
}
.card-header.data-v-4f5b6586 {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 30rpx;
	border-bottom: 1rpx solid #f0f0f0;
	background: linear-gradient(90deg, #f8f9fa 0%, #ffffff 100%);
}
.card-title.data-v-4f5b6586 {
	font-size: 32rpx;
	font-weight: bold;
	color: #333333;
}
.card-content.data-v-4f5b6586 {
	padding: 30rpx;
}
/* 标签样式 */
.tag.data-v-4f5b6586 {
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
	font-size: 24rpx;
	font-weight: bold;
}
.tag-success.data-v-4f5b6586 {
	background-color: #f0f9eb;
	color: #67C23A;
	border: 1rpx solid #c2e7b0;
}
.tag-danger.data-v-4f5b6586 {
	background-color: #fef0f0;
	color: #F56C6C;
	border: 1rpx solid #fbc4c4;
}
.tag-info.data-v-4f5b6586 {
	background-color: #f4f4f5;
	color: #909399;
	border: 1rpx solid #d3d4d6;
}
/* 按钮样式 */
.btn.data-v-4f5b6586 {
	padding: 20rpx 40rpx;
	border-radius: 25rpx;
	border: none;
	font-size: 28rpx;
	font-weight: bold;
	cursor: pointer;
	transition: all 0.3s ease;
	text-align: center;
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}
.btn.data-v-4f5b6586:hover {
	-webkit-transform: translateY(-2rpx);
	        transform: translateY(-2rpx);
	box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.15);
}
.btn-primary.data-v-4f5b6586 {
	background: linear-gradient(135deg, #409EFF 0%, #66b1ff 100%);
	color: #ffffff;
}
.btn-success.data-v-4f5b6586 {
	background: linear-gradient(135deg, #67C23A 0%, #85ce61 100%);
	color: #ffffff;
}
.btn-danger.data-v-4f5b6586 {
	background: linear-gradient(135deg, #F56C6C 0%, #f78989 100%);
	color: #ffffff;
}
.btn-warning.data-v-4f5b6586 {
	background: linear-gradient(135deg, #E6A23C 0%, #ebb563 100%);
	color: #ffffff;
}
.btn-secondary.data-v-4f5b6586 {
	background: linear-gradient(135deg, #909399 0%, #a6a9ad 100%);
	color: #ffffff;
}
.btn-disabled.data-v-4f5b6586 {
	background-color: #c0c4cc;
	color: #ffffff;
	cursor: not-allowed;
	opacity: 0.6;
}
.btn-disabled.data-v-4f5b6586:hover {
	-webkit-transform: none;
	        transform: none;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}
/* 激活码输入区域 */
.token-input-section.data-v-4f5b6586 {
	padding: 20rpx 0;
}
.input-group.data-v-4f5b6586 {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
	margin-bottom: 30rpx;
}
.token-input.data-v-4f5b6586 {
	height: 80rpx;
	padding: 0 20rpx;
	border: 2rpx solid #e0e0e0;
	border-radius: 40rpx;
	font-size: 28rpx;
	background-color: #ffffff;
	transition: all 0.3s ease;
}
.token-input.data-v-4f5b6586:focus {
	border-color: #409EFF;
	box-shadow: 0 0 0 4rpx rgba(64, 158, 255, 0.1);
}
.get-token-section.data-v-4f5b6586 {
	text-align: center;
}
.divider.data-v-4f5b6586 {
	margin: 30rpx 0;
	font-size: 24rpx;
	color: #999999;
	position: relative;
}
.divider.data-v-4f5b6586::before,
.divider.data-v-4f5b6586::after {
	content: '';
	position: absolute;
	top: 50%;
	width: 30%;
	height: 1rpx;
	background-color: #e0e0e0;
}
.divider.data-v-4f5b6586::before {
	left: 0;
}
.divider.data-v-4f5b6586::after {
	right: 0;
}
/* 激活码信息区域 */
.token-info-section.data-v-4f5b6586 {
	padding: 20rpx 0;
}
.alert.data-v-4f5b6586 {
	display: flex;
	align-items: center;
	padding: 20rpx;
	border-radius: 8rpx;
	margin-bottom: 30rpx;
}
.alert-warning.data-v-4f5b6586 {
	background-color: #fdf6ec;
	border: 1rpx solid #f5dab1;
}
.alert-icon.data-v-4f5b6586 {
	font-size: 32rpx;
	margin-right: 15rpx;
}
.alert-text.data-v-4f5b6586 {
	flex: 1;
	font-size: 26rpx;
	color: #E6A23C;
}
.info-list.data-v-4f5b6586 {
	margin-bottom: 30rpx;
}
.info-item.data-v-4f5b6586 {
	display: flex;
	align-items: center;
	padding: 20rpx 0;
	border-bottom: 1rpx solid #f0f0f0;
}
.info-item.data-v-4f5b6586:last-child {
	border-bottom: none;
}
.info-label.data-v-4f5b6586 {
	width: 150rpx;
	font-size: 28rpx;
	color: #666666;
}
.info-value.data-v-4f5b6586 {
	flex: 1;
	font-size: 28rpx;
	font-weight: bold;
	color: #333333;
}
.info-value-group.data-v-4f5b6586 {
	flex: 1;
	display: flex;
	align-items: center;
	justify-content: space-between;
}
.copy-btn.data-v-4f5b6586 {
	padding: 10rpx;
	font-size: 32rpx;
	color: #409EFF;
	cursor: pointer;
}
/* 额度管理样式 */
.quota-main.data-v-4f5b6586 {
	display: flex;
	gap: 20rpx;
	margin-bottom: 30rpx;
}
.quota-card.data-v-4f5b6586 {
	flex: 1;
	display: flex;
	align-items: center;
	padding: 30rpx 20rpx;
	border-radius: 16rpx;
	box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.1);
	transition: all 0.3s ease;
}
.quota-card.data-v-4f5b6586:hover {
	-webkit-transform: translateY(-4rpx);
	        transform: translateY(-4rpx);
	box-shadow: 0 8rpx 25rpx rgba(0, 0, 0, 0.15);
}
.today-usage.data-v-4f5b6586 {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: #ffffff;
}
.remaining-total.data-v-4f5b6586 {
	background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
	color: #ffffff;
}
.quota-icon.data-v-4f5b6586 {
	font-size: 48rpx;
	margin-right: 20rpx;
	opacity: 0.8;
}
.quota-content.data-v-4f5b6586 {
	flex: 1;
}
.quota-value.data-v-4f5b6586 {
	font-size: 42rpx;
	font-weight: bold;
	margin-bottom: 8rpx;
	text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}
.quota-label.data-v-4f5b6586 {
	font-size: 24rpx;
	opacity: 0.9;
}
.quota-sublabel.data-v-4f5b6586 {
	font-size: 20rpx;
	opacity: 0.7;
	margin-top: 4rpx;
}
/* 详细额度信息 */
.quota-details.data-v-4f5b6586 {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	gap: 20rpx;
	margin-bottom: 30rpx;
}
.quota-detail-item.data-v-4f5b6586 {
	text-align: center;
	padding: 25rpx 15rpx;
	background-color: #f8f9fa;
	border-radius: 12rpx;
	border: 1rpx solid #e9ecef;
	transition: all 0.3s ease;
}
.quota-detail-item.data-v-4f5b6586:hover {
	background-color: #e3f2fd;
	border-color: #409EFF;
	-webkit-transform: translateY(-2rpx);
	        transform: translateY(-2rpx);
}
.quota-detail-value.data-v-4f5b6586 {
	font-size: 36rpx;
	font-weight: bold;
	color: #409EFF;
	margin-bottom: 10rpx;
}
.quota-detail-label.data-v-4f5b6586 {
	font-size: 24rpx;
	color: #666666;
}
/* 操作按钮 */
.action-buttons.data-v-4f5b6586 {
	display: flex;
	justify-content: space-around;
	gap: 15rpx;
	margin-top: 30rpx;
}
.action-buttons .btn.data-v-4f5b6586 {
	flex: 1;
	height: 70rpx;
	border-radius: 35rpx;
	font-size: 26rpx;
}
/* 导航按钮 */
.nav-buttons.data-v-4f5b6586 {
	display: flex;
	gap: 20rpx;
	margin-top: 30rpx;
}
.nav-btn.data-v-4f5b6586 {
	flex: 1;
	height: 80rpx;
	border-radius: 40rpx;
	font-size: 30rpx;
}
/* 弹窗样式 */
.modal-overlay.data-v-4f5b6586 {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0, 0, 0, 0.5);
	display: flex;
	justify-content: center;
	align-items: center;
	z-index: 1000;
}
.modal-content.data-v-4f5b6586 {
	background-color: #ffffff;
	border-radius: 16rpx;
	width: 90%;
	max-width: 600rpx;
	max-height: 80vh;
	overflow-y: auto;
	box-shadow: 0 10rpx 40rpx rgba(0, 0, 0, 0.3);
	-webkit-animation: modalSlideIn-data-v-4f5b6586 0.3s ease-out;
	        animation: modalSlideIn-data-v-4f5b6586 0.3s ease-out;
}
@-webkit-keyframes modalSlideIn-data-v-4f5b6586 {
from {
		opacity: 0;
		-webkit-transform: translateY(-50rpx) scale(0.9);
		        transform: translateY(-50rpx) scale(0.9);
}
to {
		opacity: 1;
		-webkit-transform: translateY(0) scale(1);
		        transform: translateY(0) scale(1);
}
}
@keyframes modalSlideIn-data-v-4f5b6586 {
from {
		opacity: 0;
		-webkit-transform: translateY(-50rpx) scale(0.9);
		        transform: translateY(-50rpx) scale(0.9);
}
to {
		opacity: 1;
		-webkit-transform: translateY(0) scale(1);
		        transform: translateY(0) scale(1);
}
}
.modal-header.data-v-4f5b6586 {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 30rpx;
	border-bottom: 1rpx solid #f0f0f0;
}
.modal-title.data-v-4f5b6586 {
	font-size: 32rpx;
	font-weight: bold;
	color: #333333;
}
.modal-close.data-v-4f5b6586 {
	font-size: 48rpx;
	color: #999999;
	cursor: pointer;
	width: 60rpx;
	height: 60rpx;
	display: flex;
	justify-content: center;
	align-items: center;
	border-radius: 50%;
	transition: all 0.3s ease;
}
.modal-close.data-v-4f5b6586:hover {
	background-color: #f5f5f5;
	color: #666666;
}
.modal-footer.data-v-4f5b6586 {
	padding: 30rpx;
	border-top: 1rpx solid #f0f0f0;
	display: flex;
	justify-content: center;
	gap: 20rpx;
}
/* 激活码弹窗样式 */
.token-dialog-content.data-v-4f5b6586 {
	padding: 30rpx;
}
.token-dialog-tip.data-v-4f5b6586 {
	background-color: #fdf6ec;
	color: #E6A23C;
	padding: 20rpx;
	border-radius: 8rpx;
	margin-bottom: 30rpx;
	text-align: center;
	font-size: 26rpx;
	border-left: 6rpx solid #E6A23C;
}
.token-dialog-code.data-v-4f5b6586 {
	display: flex;
	align-items: center;
	justify-content: center;
	margin-bottom: 30rpx;
	background-color: #f0f9eb;
	padding: 20rpx;
	border-radius: 8rpx;
	border-left: 6rpx solid #67C23A;
}
.token-dialog-label.data-v-4f5b6586 {
	font-size: 28rpx;
	color: #333333;
	margin-right: 15rpx;
}
.token-dialog-value-group.data-v-4f5b6586 {
	display: flex;
	align-items: center;
	gap: 15rpx;
}
.token-dialog-value.data-v-4f5b6586 {
	font-size: 26rpx;
	font-weight: bold;
	color: #67C23A;
	word-break: break-all;
}
.token-dialog-steps.data-v-4f5b6586 {
	margin-top: 30rpx;
	text-align: left;
	background-color: #f5f7fa;
	padding: 30rpx;
	border-radius: 12rpx;
}
.step-item.data-v-4f5b6586 {
	display: flex;
	align-items: center;
	padding: 20rpx 0;
	border-bottom: 1rpx solid #e0e0e0;
}
.step-item.data-v-4f5b6586:last-child {
	border-bottom: none;
}
.step-item.completed.data-v-4f5b6586 {
	color: #67C23A;
}
.step-number.data-v-4f5b6586 {
	width: 50rpx;
	height: 50rpx;
	background-color: #409EFF;
	color: #ffffff;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-weight: bold;
	margin-right: 20rpx;
	font-size: 24rpx;
}
.step-item.completed .step-number.data-v-4f5b6586 {
	background-color: #67C23A;
}
.step-text.data-v-4f5b6586 {
	flex: 1;
	font-size: 26rpx;
}
.step-status.data-v-4f5b6586 {
	font-size: 32rpx;
	margin-left: 20rpx;
}
/* 赞助弹窗样式 */
.donate-dialog-content.data-v-4f5b6586 {
	padding: 30rpx 20rpx;
}
.donate-header.data-v-4f5b6586 {
	text-align: center;
	margin-bottom: 30rpx;
}
.donate-title.data-v-4f5b6586 {
	font-size: 36rpx;
	font-weight: bold;
	color: #333333;
	margin-bottom: 20rpx;
}
.donate-description.data-v-4f5b6586 {
	font-size: 28rpx;
	color: #666666;
	line-height: 1.6;
	background-color: #f8f8f8;
	padding: 20rpx;
	border-radius: 8rpx;
}
.donate-methods.data-v-4f5b6586 {
	display: flex;
	justify-content: space-around;
	margin: 30rpx 0;
	gap: 30rpx;
}
.donate-method.data-v-4f5b6586 {
	flex: 1;
	text-align: center;
}
.method-title.data-v-4f5b6586 {
	font-size: 28rpx;
	font-weight: bold;
	color: #333333;
	margin-bottom: 20rpx;
}
.qr-placeholder.data-v-4f5b6586 {
	width: 200rpx;
	height: 200rpx;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: #ffffff;
	border-radius: 12rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 24rpx;
	margin: 0 auto;
	box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.1);
}
.donate-footer.data-v-4f5b6586 {
	border-top: 1rpx solid #e0e0e0;
	padding-top: 20rpx;
	text-align: center;
}
.donate-tip.data-v-4f5b6586 {
	font-size: 24rpx;
	color: #999999;
	margin: 10rpx 0;
	line-height: 1.5;
}
/* 响应式设计 */
@media screen and (max-width: 750rpx) {
.quota-main.data-v-4f5b6586 {
		flex-direction: column;
}
.quota-details.data-v-4f5b6586 {
		grid-template-columns: repeat(2, 1fr);
}
.action-buttons.data-v-4f5b6586 {
		flex-direction: column;
}
.donate-methods.data-v-4f5b6586 {
		flex-direction: column;
		align-items: center;
}
}
@media screen and (max-width: 600rpx) {
.quota-details.data-v-4f5b6586 {
		grid-template-columns: 1fr;
}
}

