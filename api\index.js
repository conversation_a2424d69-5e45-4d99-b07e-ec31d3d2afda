import http from '@/utils/request'

// 签到相关API
export const signInApi = {
	// 获取签到配置
	getSetting() {
		return http.get('/sign_in/setting')
	},

	// 检查今天是否签到
	checkSignInToday(tokenName) {
		return http.get('/sign_in/check_sign_in', {
			tokenName
		})
	},

	// 获取签到历史
	getHistory(tokenName, pageNum = 1, pageSize = 10) {
		return http.get('/sign_in/history', {
			tokenName,
			pageNum,
			pageSize
		})
	},

	// 执行签到
	doSignIn(tokenName) {
		return http.post('/sign_in/do_sign_in', {
			tokenName: tokenName,
			client: "gzh"
		}, {
			header: {
				'Content-Type': 'application/x-www-form-urlencoded'
			}
		})
	}
}

// 激活码相关API
export const tokenApi = {
	// 获取激活码额度信息
	getQuota(tokenName) {
		return http.get('/fq_token/get_quota', {
			tokenName
		})
	},

	// 获取激活码详细信息
	getTokenInfo(tokenName) {
		return http.get('/fq_token/selectByPage', {
			tokenName,
			pageNum: 1,
			pageSize: 1
		})
	},

	// 刷新额度
	refreshQuota(tokenName) {
		return http.get('/fq_token/get_quota', {
			tokenName
		})
	},

	// 新增激活码
	addToken(tokenName, tokenTime) {
		return http.put('/fq_token/insert', {
			tokenName,
			tokenTime,
			client: "gzh"
		})
	},

	// 获取用户当前token状态
	getUserToken() {
		return http.get('/fq_token/get_user_token')
	}
}

// 引入 Base64 工具
import {
	base64Encode as customBase64Encode,
	base64Decode as customBase64Decode
} from '@/utils/base64'

// 工具函数
export const utils = {
	// Base64 编码函数 (使用自定义实现)
	base64Encode(str) {
		try {
			// 优先使用原生函数
			if (typeof uni.base64Encode === 'function') {
				return uni.base64Encode(str)
			}

			// 使用自定义 Base64 实现
			return customBase64Encode(String(str))
		} catch (error) {
			console.error('Base64编码失败:', error)
			return String(str)
		}
	},

	// Base64 解码函数 (使用自定义实现)
	base64Decode(str) {
		try {
			// 优先使用原生函数
			if (typeof uni.base64Decode === 'function') {
				return uni.base64Decode(str)
			}

			// 使用自定义 Base64 实现
			return customBase64Decode(String(str))
		} catch (error) {
			console.error('Base64解码失败:', error)
			return String(str)
		}
	},

	// 简单编码函数 (十六进制编码，作为备用方案)
	simpleEncode(str) {
		try {
			str = String(str)
			let result = ''
			for (let i = 0; i < str.length; i++) {
				const hex = str.charCodeAt(i).toString(16)
				result += hex.padStart(2, '0')
			}
			return result
		} catch (error) {
			console.error('简单编码失败:', error)
			return String(str)
		}
	},

	// 简单解码函数 (十六进制解码，作为备用方案)
	simpleDecode(hexStr) {
		try {
			hexStr = String(hexStr)
			let result = ''
			for (let i = 0; i < hexStr.length; i += 2) {
				const hex = hexStr.substr(i, 2)
				const charCode = parseInt(hex, 16)
				if (!isNaN(charCode) && charCode > 0) {
					result += String.fromCharCode(charCode)
				}
			}
			return result
		} catch (error) {
			console.error('简单解码失败:', error)
			return String(hexStr)
		}
	},


	// 加密函数 (使用 Base64 编码)
	encrypt(data, key = 'default_key', iv = 'default_iv') {
		try {
			// 处理对象类型
			if (typeof data === "object") {
				try {
					data = JSON.stringify(data)
				} catch (error) {
					console.log("JSON序列化失败:", error)
					return data
				}
			}

			// 转换为字符串
			const dataStr = String(data)

			// 使用异或加密
			const keyStr = key + iv
			let encrypted = ''

			for (let i = 0; i < dataStr.length; i++) {
				const dataChar = dataStr.charCodeAt(i)
				const keyChar = keyStr.charCodeAt(i % keyStr.length)
				// 异或运算加密
				const encryptedChar = dataChar ^ keyChar
				encrypted += String.fromCharCode(encryptedChar)
			}

			// 使用 Base64 编码
			return this.base64Encode(encrypted)
		} catch (error) {
			console.error('加密失败:', error)
			// 降级到简单编码
			try {
				return this.simpleEncode(String(data))
			} catch (fallbackError) {
				console.error('降级编码也失败:', fallbackError)
				return String(data)
			}
		}
	},

	// 解密函数 (使用 Base64 解码)
	decrypt(encryptedData, key = 'default_key', iv = 'default_iv') {
		try {
			if (!encryptedData) return ''

			// 使用 Base64 解码
			let decoded
			try {
				decoded = this.base64Decode(encryptedData)
			} catch (base64Error) {
				console.error('Base64解码失败，尝试简单解码:', base64Error)
				// 降级到简单解码
				decoded = this.simpleDecode(encryptedData)
			}

			if (!decoded) return encryptedData

			// 使用相同的密钥解密
			const keyStr = key + iv
			let decrypted = ''

			for (let i = 0; i < decoded.length; i++) {
				const encryptedChar = decoded.charCodeAt(i)
				const keyChar = keyStr.charCodeAt(i % keyStr.length)
				// 异或运算解密
				const decryptedChar = encryptedChar ^ keyChar
				decrypted += String.fromCharCode(decryptedChar)
			}

			return decrypted
		} catch (error) {
			console.error('解密失败:', error)
			return String(encryptedData)
		}
	},



	// 获取当前时间字符串
	getCurrentTimeString() {
		const now = new Date()
		const year = now.getFullYear()
		const month = String(now.getMonth() + 1).padStart(2, '0')
		const day = String(now.getDate()).padStart(2, '0')
		const hours = String(now.getHours()).padStart(2, '0')
		const minutes = String(now.getMinutes()).padStart(2, '0')
		const seconds = String(now.getSeconds()).padStart(2, '0')

		return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
	},

	// 复制文本到剪贴板
	copyText(text) {
		return new Promise((resolve, reject) => {
			// #ifdef MP-WEIXIN
			// 微信小程序需要检查隐私授权
			if (typeof wx !== 'undefined' && wx.getPrivacySetting) {
				wx.getPrivacySetting({
					success: (res) => {
						if (res.needAuthorization) {
							// 需要用户授权，触发隐私授权流程
							wx.requirePrivacyAuthorize({
								success: () => {
									this.performCopy(text, resolve, reject)
								},
								fail: (err) => {
									console.error('隐私授权失败:', err)
									uni.showToast({
										title: '需要授权才能使用复制功能',
										icon: 'none',
										duration: 2000
									})
									reject(err)
								}
							})
						} else {
							// 已授权，直接复制
							this.performCopy(text, resolve, reject)
						}
					},
					fail: (err) => {
						console.error('获取隐私设置失败:', err)
						// 获取失败时尝试直接复制
						this.performCopy(text, resolve, reject)
					}
				})
			} else {
				// 非微信小程序或不支持隐私检查，直接复制
				this.performCopy(text, resolve, reject)
			}
			// #endif

			// #ifndef MP-WEIXIN
			// 非微信小程序平台，直接复制
			this.performCopy(text, resolve, reject)
			// #endif
		})
	},

	// 执行复制操作
	performCopy(text, resolve, reject) {
		uni.setClipboardData({
			data: text,
			success: () => {
				uni.showToast({
					title: '复制成功',
					icon: 'success',
					duration: 1500
				})
				resolve()
			},
			fail: (err) => {
				console.error('复制失败详细信息:', err)
				let errorMsg = '复制失败'

				// 根据错误类型提供更具体的提示
				if (err.errMsg && err.errMsg.includes('api scope is not declared')) {
					errorMsg = '复制功能需要隐私授权，请重新授权'
				} else if (err.errMsg && err.errMsg.includes('permission denied')) {
					errorMsg = '复制权限被拒绝，请检查应用权限设置'
				}

				uni.showToast({
					title: errorMsg,
					icon: 'none',
					duration: 2000
				})
				reject(err)
			}
		})
	},

	// 格式化日期
	formatDate(date, format = 'YYYY-MM-DD') {
		const d = new Date(date)
		const year = d.getFullYear()
		const month = String(d.getMonth() + 1).padStart(2, '0')
		const day = String(d.getDate()).padStart(2, '0')
		const hours = String(d.getHours()).padStart(2, '0')
		const minutes = String(d.getMinutes()).padStart(2, '0')
		const seconds = String(d.getSeconds()).padStart(2, '0')

		return format
			.replace('YYYY', year)
			.replace('MM', month)
			.replace('DD', day)
			.replace('HH', hours)
			.replace('mm', minutes)
			.replace('ss', seconds)
	}
}