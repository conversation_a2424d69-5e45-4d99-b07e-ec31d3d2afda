{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/projects/HBuilder/fq_sign_uiapp/pages/signin/signin.vue?b2ca", "webpack:///D:/projects/HBuilder/fq_sign_uiapp/pages/signin/signin.vue?ef42", "webpack:///D:/projects/HBuilder/fq_sign_uiapp/pages/signin/signin.vue?ea32", "webpack:///D:/projects/HBuilder/fq_sign_uiapp/pages/signin/signin.vue?09d1", "uni-app:///pages/signin/signin.vue", "webpack:///D:/projects/HBuilder/fq_sign_uiapp/pages/signin/signin.vue?8cf3", "webpack:///D:/projects/HBuilder/fq_sign_uiapp/pages/signin/signin.vue?7173"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "userInfo", "hasSigned", "signInConfig", "dailySignInReward", "enableContinuousReward", "continuousDaysRequired", "continuousReward", "userStats", "continuousDays", "signInHistory", "historyPage", "historyPageSize", "hasMoreHistory", "currentDate", "weekDays", "calendarDates", "signInDates", "computed", "currentYear", "currentMonth", "onLoad", "methods", "initPage", "tokenApi", "tokenRes", "tokenName", "res", "tokenTime", "tokenStatus", "tokenCompCode", "detailRes", "tokenDetail", "console", "loadSignInConfig", "signInApi", "loadUserSignInInfo", "signRes", "loadSignInHistory", "calculateContinuous", "pageSize", "rows", "historyList", "calculateContinuousDays", "today", "signInDate", "lastDate", "yesterday", "expectedDate", "generateMonthlyRecord", "map", "filter", "handleSignIn", "uni", "title", "icon", "duration", "setTimeout", "loadMoreHistory", "generateCalendar", "dates", "day", "i", "date", "isToday", "signed", "disabled", "formatTime", "goToActivation", "url", "goBack"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA+H;AAC/H;AAC0D;AACL;AACqC;;;AAG1F;AACoL;AACpL,gBAAgB,2LAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,6FAAM;AACR,EAAE,sGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,iGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACnCA;AAAA;AAAA;AAAA;AAAwrB,CAAgB,srBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;AC6G5sB;AAKA;AAEA;AAAA;AAAA,eAEA;EACAC;IACA;MACA;MACAC;MAEA;MACAC;MACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACAC;QACAC;MACA;MACAC;MACAC;MACAC;MACAC;MAEA;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACAC;MACA;IACA;IACAC;MACA;IACA;EACA;EAEAC;IACA;IACA;MACA;IACA;IACA;EACA;EAEAC;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAGAC;cAAA;gBAAAC;gBAAA,MACAA;kBAAA;kBAAA;gBAAA;gBACAC;gBAAA,KACAA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OAEAF;cAAA;gBAAAG;gBAAA,MACAA;kBAAA;kBAAA;gBAAA;gBACA;kBACAD;kBACAE;kBACAC;kBACAC;gBACA;;gBAEA;gBAAA;gBAAA;gBAAA,OAEAN;cAAA;gBAAAO;gBACA;kBACAC;kBACA,iDACA,iBACAA,YACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAC;cAAA;gBAAA;gBAAA,OAIA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAKAA;cAAA;gBAAA;gBAAA,OAIA;cAAA;gBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEAC;cAAA;gBAAAR;gBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAM;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAG;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA,IAEA;kBAAA;kBAAA;gBAAA;gBACAH;gBAAA;cAAA;gBAAA;gBAAA;gBAAA,OAMAE;cAAA;gBAAAE;gBAEA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAJ;gBACA;cAAA;gBAAA;gBAAA,OAIA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAK;MAAA;QAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAAC;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAGAC;gBAAA;gBAAA,OACAL,4BACA,2BACA,oBACAK,SACA;cAAA;gBAJAb;gBAMA;kBACA;oBACA,oKACAc,MACA;oBACA;kBACA;oBACAC,8BAEA;oBACA;;oBAEA;oBACA;;oBAEA;oBACA;oBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAT;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAU;MACA;QACA;QACA;MACA;;MAEA;MACAD;QAAA;MAAA;MAEA;MACAE;MAEA;MACA;MAEA;QACA;QACAC;QAEA;UACA;UACA;YACApC;YACAqC;UACA;YACA;YACAC;YACA;cACAtC;cACAqC;YACA;cACA;YACA;UACA;QACA;UACA;UACAE;UAEA;YACAvC;YACAqC;UACA;YACA;UACA;QACA;MACA;MAEA;IACA;IAEA;IACAG;MACA;QACA;QACA;QACA;MACA;MAEA;MACA;MAEA,+BACAC;QAAA;MAAA,GACAC;QAAA;MAAA;MAEA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA,IAEA;kBAAA;kBAAA;gBAAA;gBACAC;kBACAC;kBACAC;gBACA;gBAAA;cAAA;gBAAA;gBAAA;gBAAA,OAKApB;cAAA;gBAAAR;gBAEA;kBACA0B;oBACAC;oBACAC;oBACAC;kBACA;kBAEA;;kBAEA;kBACAZ;kBACA;;kBAEA;kBACA;;kBAEA;kBACA;;kBAEA;kBACAa;oBACA;oBACA;kBACA;gBACA;kBACAJ;oBACAC;oBACAC;oBACAC;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAvB;gBACAoB;kBACAC;kBACAC;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAE;MACA;MACA;IACA;IAEA;IACAC;MAAA;MACA;MACA;MAEA;MACA;MAEA;MACA;MAEA;;MAEA;MACA;QACAC;UACAC;QACA;MACA;;MAEA;MAAA,2BACAC;QACA;QACA;QACA;UAAA,OACAC,+BACAA,6BACAA;QAAA,EACA;QAEA;QAEAH;UACAC;UACAG;UACAC;UACAC;QACA;MAAA;MAhBA;QAAA;MAiBA;MAEA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACAf;QACAgB;MACA;IACA;IAEA;IACAC;MACAjB;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC9dA;AAAA;AAAA;AAAA;AAAqgC,CAAgB,+9BAAG,EAAC,C;;;;;;;;;;;ACAzhC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/signin/signin.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/signin/signin.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./signin.vue?vue&type=template&id=23701386&scoped=true&\"\nvar renderjs\nimport script from \"./signin.vue?vue&type=script&lang=js&\"\nexport * from \"./signin.vue?vue&type=script&lang=js&\"\nimport style0 from \"./signin.vue?vue&type=style&index=0&id=23701386&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"23701386\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/signin/signin.vue\"\nexport default component.exports", "export * from \"-!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./signin.vue?vue&type=template&id=23701386&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.__map(_vm.calendarDates, function (date, __i1__) {\n    var $orig = _vm.__get_orig(date)\n    var g0 = date.day || Math.random()\n    return {\n      $orig: $orig,\n      g0: g0,\n    }\n  })\n  var l1 = _vm.__map(_vm.signInHistory, function (item, __i2__) {\n    var $orig = _vm.__get_orig(item)\n    var m0 = _vm.formatTime(item.signInTime)\n    return {\n      $orig: $orig,\n      m0: m0,\n    }\n  })\n  var g1 = _vm.signInHistory.length\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n        l1: l1,\n        g1: g1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./signin.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./signin.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"container\">\r\n\t\t<!-- 页面头部 -->\r\n\t\t<view class=\"header\">\r\n\t\t\t<view class=\"header-title\">每日签到</view>\r\n\t\t\t<view class=\"header-subtitle\">坚持签到，获得更多额度</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 签到卡片 -->\r\n\t\t<view class=\"card fade-in\">\r\n\t\t\t<view class=\"card-header\">\r\n\t\t\t\t<view class=\"card-title\">每日签到</view>\r\n\t\t\t\t<view class=\"tag\" :class=\"hasSigned ? 'tag-success' : 'tag-info'\">\r\n\t\t\t\t\t{{ hasSigned ? '今日已签到' : '未签到' }}\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"card-content\">\r\n\t\t\t\t<!-- 签到信息 -->\r\n\t\t\t\t<view class=\"signin-info\">\r\n\t\t\t\t\t<view class=\"signin-stats\">\r\n\t\t\t\t\t\t<view class=\"stat-item\">\r\n\t\t\t\t\t\t\t<view class=\"stat-number\">{{ userStats.continuousDays || 0 }}</view>\r\n\t\t\t\t\t\t\t<view class=\"stat-label\">连续签到天数</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"stat-item\">\r\n\t\t\t\t\t\t\t<view class=\"stat-number\">{{ signInConfig.dailySignInReward || 5 }}</view>\r\n\t\t\t\t\t\t\t<view class=\"stat-label\">每日奖励额度</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<view class=\"signin-tip\" v-if=\"signInConfig.enableContinuousReward\">\r\n\t\t\t\t\t\t连续签到 {{ signInConfig.continuousDaysRequired || 7 }} 天可额外获得\r\n\t\t\t\t\t\t{{ signInConfig.continuousReward || 15 }} 额度\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- 签到日历 -->\r\n\t\t\t\t<view class=\"calendar-container\">\r\n\t\t\t\t\t<view class=\"calendar-header\">\r\n\t\t\t\t\t\t<view class=\"calendar-title\">{{ currentYear }}年{{ currentMonth }}月</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"calendar-weekdays\">\r\n\t\t\t\t\t\t<view class=\"weekday\" v-for=\"day in weekDays\" :key=\"day\">{{ day }}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"calendar-dates\">\r\n\t\t\t\t\t\t<view v-for=\"date in calendarDates\" :key=\"date.day || Math.random()\" class=\"calendar-date\"\r\n\t\t\t\t\t\t\t:class=\"{\r\n\t\t\t\t\t\t\t\t'empty': !date.day,\r\n\t\t\t\t\t\t\t\t'signed': date.signed,\r\n\t\t\t\t\t\t\t\t'today': date.isToday,\r\n\t\t\t\t\t\t\t\t'disabled': date.disabled\r\n\t\t\t\t\t\t\t}\">\r\n\t\t\t\t\t\t\t<text v-if=\"date.day\">{{ date.day }}</text>\r\n\t\t\t\t\t\t\t<view v-if=\"date.signed\" class=\"signed-mark\">✓</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- 签到按钮 -->\r\n\t\t\t\t<button class=\"btn signin-btn\" :class=\"hasSigned || !userInfo ? 'btn-disabled' : 'btn-primary'\"\r\n\t\t\t\t\t:disabled=\"hasSigned || !userInfo\" @click=\"handleSignIn\">\r\n\t\t\t\t\t{{ hasSigned ? '今日已签到' : '立即签到' }}\r\n\t\t\t\t</button>\r\n\r\n\t\t\t\t<!-- 签到历史 -->\r\n\t\t\t\t<view class=\"signin-history\">\r\n\t\t\t\t\t<view class=\"history-header\">\r\n\t\t\t\t\t\t<view class=\"history-title\">签到历史</view>\r\n\t\t\t\t\t\t<view class=\"history-more\" @click=\"loadMoreHistory\" v-if=\"hasMoreHistory\">\r\n\t\t\t\t\t\t\t查看更多\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"history-list\">\r\n\t\t\t\t\t\t<view v-for=\"item in signInHistory\" :key=\"item.id\" class=\"history-item\"\r\n\t\t\t\t\t\t\t:class=\"item.extraReward ? 'extra-reward' : ''\">\r\n\t\t\t\t\t\t\t<view class=\"history-content\">\r\n\t\t\t\t\t\t\t\t<view class=\"history-reward\">获得{{ item.rewardAmount }}额度</view>\r\n\t\t\t\t\t\t\t\t<view class=\"tag tag-success\" v-if=\"item.extraReward\">连续签到奖励</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"history-time\">{{ formatTime(item.signInTime) }}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view v-if=\"signInHistory.length === 0\" class=\"no-data\">\r\n\t\t\t\t\t\t\t暂无签到记录\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 提示卡片 -->\r\n\t\t<view v-if=\"!userInfo\" class=\"card fade-in\" style=\"animation-delay: 0.1s;\">\r\n\t\t\t<view class=\"card-content\">\r\n\t\t\t\t<view class=\"no-user-tip\">\r\n\t\t\t\t\t<view class=\"tip-icon\">🔑</view>\r\n\t\t\t\t\t<view class=\"tip-text\">请先验证激活码后再进行签到</view>\r\n\t\t\t\t\t<button class=\"btn btn-primary\" @click=\"goToActivation\">前往激活码管理</button>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 导航按钮 -->\r\n\t\t<view class=\"nav-buttons\">\r\n\t\t\t<button class=\"btn btn-primary nav-btn\" @click=\"goToActivation\">激活码管理</button>\r\n\t\t\t<button class=\"btn btn-secondary nav-btn\" @click=\"goBack\">返回首页</button>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport {\r\n\t\tsignInApi,\r\n\t\ttokenApi,\r\n\t\tutils\r\n\t} from '@/api/index'\r\n\timport {\r\n\t\tcheckLogin\r\n\t} from '@/utils/auth'\r\n\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\t// 用户信息\r\n\t\t\t\tuserInfo: null,\r\n\r\n\t\t\t\t// 签到相关\r\n\t\t\t\thasSigned: false,\r\n\t\t\t\tsignInConfig: {\r\n\t\t\t\t\tdailySignInReward: 5,\r\n\t\t\t\t\tenableContinuousReward: false,\r\n\t\t\t\t\tcontinuousDaysRequired: 7,\r\n\t\t\t\t\tcontinuousReward: 15\r\n\t\t\t\t},\r\n\t\t\t\tuserStats: {\r\n\t\t\t\t\tcontinuousDays: 0\r\n\t\t\t\t},\r\n\t\t\t\tsignInHistory: [],\r\n\t\t\t\thistoryPage: 1,\r\n\t\t\t\thistoryPageSize: 5,\r\n\t\t\t\thasMoreHistory: false,\r\n\r\n\t\t\t\t// 日历相关\r\n\t\t\t\tcurrentDate: new Date(),\r\n\t\t\t\tweekDays: ['日', '一', '二', '三', '四', '五', '六'],\r\n\t\t\t\tcalendarDates: [],\r\n\t\t\t\tsignInDates: []\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\tcomputed: {\r\n\t\t\tcurrentYear() {\r\n\t\t\t\treturn this.currentDate.getFullYear()\r\n\t\t\t},\r\n\t\t\tcurrentMonth() {\r\n\t\t\t\treturn this.currentDate.getMonth() + 1\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\tonLoad() {\r\n\t\t\t// 检查登录状态\r\n\t\t\tif (!checkLogin()) {\r\n\t\t\t\treturn\r\n\t\t\t}\r\n\t\t\tthis.initPage()\r\n\t\t},\r\n\r\n\t\tmethods: {\r\n\t\t\t// 初始化页面\r\n\t\t\tasync initPage() {\r\n\t\t\t\ttry {\r\n\t\t\t\t\t// 获取用户token状态\r\n\t\t\t\t\tconst tokenRes = await tokenApi.getUserToken()\r\n\t\t\t\t\tif (tokenRes.code === 200 && tokenRes.data) {\r\n\t\t\t\t\t\tconst tokenName = tokenRes.data.tokenName\r\n\t\t\t\t\t\tif (tokenName) {\r\n\t\t\t\t\t\t\t// 验证激活码获取用户信息\r\n\t\t\t\t\t\t\tconst res = await tokenApi.getQuota(tokenName)\r\n\t\t\t\t\t\t\tif (res.code === 200) {\r\n\t\t\t\t\t\t\t\tthis.userInfo = {\r\n\t\t\t\t\t\t\t\t\ttokenName: tokenName,\r\n\t\t\t\t\t\t\t\t\ttokenTime: utils.formatDate(new Date(), 'YYYY-MM-DD'),\r\n\t\t\t\t\t\t\t\t\ttokenStatus: 0,\r\n\t\t\t\t\t\t\t\t\ttokenCompCode: '尚未绑定'\r\n\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\t// 尝试获取额外的token信息\r\n\t\t\t\t\t\t\t\ttry {\r\n\t\t\t\t\t\t\t\t\tconst detailRes = await tokenApi.getTokenInfo(tokenName)\r\n\t\t\t\t\t\t\t\t\tif (detailRes.code === 200 && detailRes.rows && detailRes.rows.length > 0) {\r\n\t\t\t\t\t\t\t\t\t\tconst tokenDetail = detailRes.rows[0]\r\n\t\t\t\t\t\t\t\t\t\tthis.userInfo = {\r\n\t\t\t\t\t\t\t\t\t\t\t...this.userInfo,\r\n\t\t\t\t\t\t\t\t\t\t\t...tokenDetail\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t} catch (error) {\r\n\t\t\t\t\t\t\t\t\tconsole.error('获取token详情失败:', error)\r\n\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\t// 加载签到信息\r\n\t\t\t\t\t\t\t\tawait this.loadUserSignInInfo()\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error('获取用户token失败:', error)\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// 获取签到配置\r\n\t\t\t\tawait this.loadSignInConfig()\r\n\t\t\t\t// 生成日历数据\r\n\t\t\t\tthis.generateCalendar()\r\n\t\t\t},\r\n\r\n\t\t\t// 获取签到配置\r\n\t\t\tasync loadSignInConfig() {\r\n\t\t\t\ttry {\r\n\t\t\t\t\tconst res = await signInApi.getSetting()\r\n\t\t\t\t\tif (res.code === 200) {\r\n\t\t\t\t\t\tthis.signInConfig = res.data\r\n\t\t\t\t\t}\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error('获取签到配置失败:', error)\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t// 加载用户签到信息\r\n\t\t\tasync loadUserSignInInfo() {\r\n\t\t\t\tif (!this.userInfo) return\r\n\r\n\t\t\t\tif (!this.userInfo.tokenName) {\r\n\t\t\t\t\tconsole.error('tokenName为空，无法加载签到信息')\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\r\n\t\t\t\ttry {\r\n\t\t\t\t\t// 检查今日是否已签到\r\n\t\t\t\t\tconst signRes = await signInApi.checkSignInToday(this.userInfo.tokenName)\r\n\r\n\t\t\t\t\tif (signRes.code === 200) {\r\n\t\t\t\t\t\tthis.hasSigned = signRes.data\r\n\t\t\t\t\t}\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error('检查签到状态失败:', error)\r\n\t\t\t\t\tthis.hasSigned = false\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// 加载签到历史\r\n\t\t\t\tawait this.loadSignInHistory(true)\r\n\t\t\t},\r\n\r\n\t\t\t// 加载签到历史\r\n\t\t\tasync loadSignInHistory(calculateContinuous = false) {\r\n\t\t\t\tif (!this.userInfo) return\r\n\r\n\t\t\t\ttry {\r\n\t\t\t\t\tconst pageSize = calculateContinuous ? 30 : this.historyPageSize\r\n\t\t\t\t\tconst res = await signInApi.getHistory(\r\n\t\t\t\t\t\tthis.userInfo.tokenName,\r\n\t\t\t\t\t\tthis.historyPage,\r\n\t\t\t\t\t\tpageSize\r\n\t\t\t\t\t)\r\n\r\n\t\t\t\t\tif (res.code === 200) {\r\n\t\t\t\t\t\tif (!calculateContinuous) {\r\n\t\t\t\t\t\t\tthis.signInHistory = this.historyPage === 1 ? res.rows : [...this.signInHistory, ...res\r\n\t\t\t\t\t\t\t\t.rows\r\n\t\t\t\t\t\t\t]\r\n\t\t\t\t\t\t\tthis.hasMoreHistory = this.signInHistory.length < res.total\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tconst historyList = res.rows || []\r\n\r\n\t\t\t\t\t\t\t// 生成当月签到记录\r\n\t\t\t\t\t\t\tthis.generateMonthlyRecord(historyList)\r\n\r\n\t\t\t\t\t\t\t// 计算连续签到天数\r\n\t\t\t\t\t\t\tthis.calculateContinuousDays(historyList)\r\n\r\n\t\t\t\t\t\t\t// 更新历史记录显示\r\n\t\t\t\t\t\t\tthis.signInHistory = historyList.slice(0, this.historyPageSize)\r\n\t\t\t\t\t\t\tthis.hasMoreHistory = historyList.length > this.historyPageSize\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error('获取签到历史失败:', error)\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t// 计算连续签到天数\r\n\t\t\tcalculateContinuousDays(historyList) {\r\n\t\t\t\tif (!historyList || historyList.length === 0) {\r\n\t\t\t\t\tthis.userStats.continuousDays = 0\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// 对签到历史按日期排序（最近的在前）\r\n\t\t\t\thistoryList.sort((a, b) => new Date(b.signInTime) - new Date(a.signInTime))\r\n\r\n\t\t\t\tconst today = new Date()\r\n\t\t\t\ttoday.setHours(0, 0, 0, 0)\r\n\r\n\t\t\t\tlet continuousDays = 0\r\n\t\t\t\tlet lastDate = null\r\n\r\n\t\t\t\tfor (let i = 0; i < historyList.length; i++) {\r\n\t\t\t\t\tconst signInDate = new Date(historyList[i].signInTime)\r\n\t\t\t\t\tsignInDate.setHours(0, 0, 0, 0)\r\n\r\n\t\t\t\t\tif (i === 0) {\r\n\t\t\t\t\t\tconst isToday = signInDate.getTime() === today.getTime()\r\n\t\t\t\t\t\tif (isToday) {\r\n\t\t\t\t\t\t\tcontinuousDays = 1\r\n\t\t\t\t\t\t\tlastDate = signInDate\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tconst yesterday = new Date(today)\r\n\t\t\t\t\t\t\tyesterday.setDate(today.getDate() - 1)\r\n\t\t\t\t\t\t\tif (signInDate.getTime() === yesterday.getTime()) {\r\n\t\t\t\t\t\t\t\tcontinuousDays = 1\r\n\t\t\t\t\t\t\t\tlastDate = signInDate\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\tbreak\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tconst expectedDate = new Date(lastDate)\r\n\t\t\t\t\t\texpectedDate.setDate(expectedDate.getDate() - 1)\r\n\r\n\t\t\t\t\t\tif (signInDate.getTime() === expectedDate.getTime()) {\r\n\t\t\t\t\t\t\tcontinuousDays++\r\n\t\t\t\t\t\t\tlastDate = signInDate\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tbreak\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\tthis.userStats.continuousDays = continuousDays\r\n\t\t\t},\r\n\r\n\t\t\t// 生成当月签到记录\r\n\t\t\tgenerateMonthlyRecord(historyList) {\r\n\t\t\t\tif (!historyList || historyList.length === 0) {\r\n\t\t\t\t\tthis.signInDates = []\r\n\t\t\t\t\tthis.generateCalendar()\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\r\n\t\t\t\tconst currentYear = this.currentYear\r\n\t\t\t\tconst currentMonth = this.currentMonth - 1\r\n\r\n\t\t\t\tthis.signInDates = historyList\r\n\t\t\t\t\t.map(item => new Date(item.signInTime))\r\n\t\t\t\t\t.filter(date => date.getFullYear() === currentYear && date.getMonth() === currentMonth)\r\n\r\n\t\t\t\tthis.generateCalendar()\r\n\t\t\t},\r\n\r\n\t\t\t// 处理签到\r\n\t\t\tasync handleSignIn() {\r\n\t\t\t\tif (!this.userInfo || this.hasSigned) return\r\n\r\n\t\t\t\tif (!this.userInfo.tokenName) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '激活码信息异常，请重新进入页面',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\r\n\t\t\t\ttry {\r\n\t\t\t\t\tconst res = await signInApi.doSignIn(this.userInfo.tokenName)\r\n\r\n\t\t\t\t\tif (res.code === 200) {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: `签到成功，获得${res.data.rewardAmount}额度`,\r\n\t\t\t\t\t\t\ticon: 'success',\r\n\t\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t\t})\r\n\r\n\t\t\t\t\t\tthis.hasSigned = true\r\n\r\n\t\t\t\t\t\t// 增加今天的日期到签到日期列表\r\n\t\t\t\t\t\tconst today = new Date()\r\n\t\t\t\t\t\tthis.signInDates.push(today)\r\n\r\n\t\t\t\t\t\t// 重新生成日历\r\n\t\t\t\t\t\tthis.generateCalendar()\r\n\r\n\t\t\t\t\t\t// 更新连续签到天数\r\n\t\t\t\t\t\tthis.userStats.continuousDays += 1\r\n\r\n\t\t\t\t\t\t// 更新签到历史\r\n\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\tthis.historyPage = 1\r\n\t\t\t\t\t\t\tthis.loadSignInHistory()\r\n\t\t\t\t\t\t}, 500)\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: res.msg || '签到失败',\r\n\t\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error('签到失败:', error)\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '签到失败，请稍后再试',\r\n\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t// 加载更多历史\r\n\t\t\tloadMoreHistory() {\r\n\t\t\t\tthis.historyPage++\r\n\t\t\t\tthis.loadSignInHistory()\r\n\t\t\t},\r\n\r\n\t\t\t// 生成日历数据\r\n\t\t\tgenerateCalendar() {\r\n\t\t\t\tconst year = this.currentYear\r\n\t\t\t\tconst month = this.currentMonth - 1\r\n\r\n\t\t\t\tconst firstDay = new Date(year, month, 1).getDay()\r\n\t\t\t\tconst daysInMonth = new Date(year, month + 1, 0).getDate()\r\n\r\n\t\t\t\tconst today = new Date()\r\n\t\t\t\tconst isCurrentMonth = today.getFullYear() === year && today.getMonth() === month\r\n\r\n\t\t\t\tlet dates = []\r\n\r\n\t\t\t\t// 填充前面的空白\r\n\t\t\t\tfor (let i = 0; i < firstDay; i++) {\r\n\t\t\t\t\tdates.push({\r\n\t\t\t\t\t\tday: null\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// 填充日期\r\n\t\t\t\tfor (let i = 1; i <= daysInMonth; i++) {\r\n\t\t\t\t\tconst dateObj = new Date(year, month, i)\r\n\t\t\t\t\tconst isToday = isCurrentMonth && today.getDate() === i\r\n\t\t\t\t\tconst signed = this.signInDates.some(date =>\r\n\t\t\t\t\t\tdate.getFullYear() === year &&\r\n\t\t\t\t\t\tdate.getMonth() === month &&\r\n\t\t\t\t\t\tdate.getDate() === i\r\n\t\t\t\t\t)\r\n\r\n\t\t\t\t\tconst disabled = dateObj > today\r\n\r\n\t\t\t\t\tdates.push({\r\n\t\t\t\t\t\tday: i,\r\n\t\t\t\t\t\tisToday,\r\n\t\t\t\t\t\tsigned,\r\n\t\t\t\t\t\tdisabled\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\r\n\t\t\t\tthis.calendarDates = dates\r\n\t\t\t},\r\n\r\n\t\t\t// 格式化时间\r\n\t\t\tformatTime(time) {\r\n\t\t\t\treturn utils.formatDate(time, 'MM-DD HH:mm')\r\n\t\t\t},\r\n\r\n\t\t\t// 前往激活码页面\r\n\t\t\tgoToActivation() {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/pages/activation/activation'\r\n\t\t\t\t})\r\n\t\t\t},\r\n\r\n\t\t\t// 返回首页\r\n\t\t\tgoBack() {\r\n\t\t\t\tuni.navigateBack()\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style scoped>\r\n\t/* 页面容器 */\r\n\t.container {\r\n\t\tmin-height: 100vh;\r\n\t\tbackground: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n\t\tpadding: 20rpx;\r\n\t}\r\n\r\n\t/* 页面头部 */\r\n\t.header {\r\n\t\ttext-align: center;\r\n\t\tpadding: 40rpx 0;\r\n\t\tcolor: #ffffff;\r\n\t}\r\n\r\n\t.header-title {\r\n\t\tfont-size: 48rpx;\r\n\t\tfont-weight: bold;\r\n\t\tmargin-bottom: 10rpx;\r\n\t\ttext-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);\r\n\t}\r\n\r\n\t.header-subtitle {\r\n\t\tfont-size: 28rpx;\r\n\t\topacity: 0.9;\r\n\t}\r\n\r\n\t/* 卡片样式 */\r\n\t.card {\r\n\t\tbackground-color: #ffffff;\r\n\t\tborder-radius: 16rpx;\r\n\t\tbox-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);\r\n\t\tmargin-bottom: 30rpx;\r\n\t\toverflow: hidden;\r\n\t\ttransition: all 0.3s ease;\r\n\t}\r\n\r\n\t.card:hover {\r\n\t\ttransform: translateY(-4rpx);\r\n\t\tbox-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.15);\r\n\t}\r\n\r\n\t.fade-in {\r\n\t\tanimation: fadeInUp 0.6s ease-out;\r\n\t}\r\n\r\n\t@keyframes fadeInUp {\r\n\t\tfrom {\r\n\t\t\topacity: 0;\r\n\t\t\ttransform: translateY(30rpx);\r\n\t\t}\r\n\r\n\t\tto {\r\n\t\t\topacity: 1;\r\n\t\t\ttransform: translateY(0);\r\n\t\t}\r\n\t}\r\n\r\n\t.card-header {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: center;\r\n\t\tpadding: 30rpx;\r\n\t\tborder-bottom: 1rpx solid #f0f0f0;\r\n\t\tbackground: linear-gradient(90deg, #f8f9fa 0%, #ffffff 100%);\r\n\t}\r\n\r\n\t.card-title {\r\n\t\tfont-size: 32rpx;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #333333;\r\n\t}\r\n\r\n\t.card-content {\r\n\t\tpadding: 30rpx;\r\n\t}\r\n\r\n\t/* 标签样式 */\r\n\t.tag {\r\n\t\tpadding: 8rpx 16rpx;\r\n\t\tborder-radius: 20rpx;\r\n\t\tfont-size: 24rpx;\r\n\t\tfont-weight: bold;\r\n\t}\r\n\r\n\t.tag-success {\r\n\t\tbackground-color: #f0f9eb;\r\n\t\tcolor: #67C23A;\r\n\t\tborder: 1rpx solid #c2e7b0;\r\n\t}\r\n\r\n\t.tag-info {\r\n\t\tbackground-color: #f4f4f5;\r\n\t\tcolor: #909399;\r\n\t\tborder: 1rpx solid #d3d4d6;\r\n\t}\r\n\r\n\t/* 按钮样式 */\r\n\t.btn {\r\n\t\tpadding: 20rpx 40rpx;\r\n\t\tborder-radius: 25rpx;\r\n\t\tborder: none;\r\n\t\tfont-size: 28rpx;\r\n\t\tfont-weight: bold;\r\n\t\tcursor: pointer;\r\n\t\ttransition: all 0.3s ease;\r\n\t\ttext-align: center;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tbox-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);\r\n\t}\r\n\r\n\t.btn:hover {\r\n\t\ttransform: translateY(-2rpx);\r\n\t\tbox-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.15);\r\n\t}\r\n\r\n\t.btn-primary {\r\n\t\tbackground: linear-gradient(135deg, #409EFF 0%, #66b1ff 100%);\r\n\t\tcolor: #ffffff;\r\n\t}\r\n\r\n\t.btn-secondary {\r\n\t\tbackground: linear-gradient(135deg, #909399 0%, #a6a9ad 100%);\r\n\t\tcolor: #ffffff;\r\n\t}\r\n\r\n\t.btn-disabled {\r\n\t\tbackground-color: #c0c4cc;\r\n\t\tcolor: #ffffff;\r\n\t\tcursor: not-allowed;\r\n\t\topacity: 0.6;\r\n\t}\r\n\r\n\t.btn-disabled:hover {\r\n\t\ttransform: none;\r\n\t\tbox-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);\r\n\t}\r\n\r\n\t/* 签到信息 */\r\n\t.signin-info {\r\n\t\tmargin-bottom: 30rpx;\r\n\t}\r\n\r\n\t.signin-stats {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-around;\r\n\t\tmargin-bottom: 20rpx;\r\n\t}\r\n\r\n\t.stat-item {\r\n\t\ttext-align: center;\r\n\t\tflex: 1;\r\n\t}\r\n\r\n\t.stat-number {\r\n\t\tfont-size: 48rpx;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #409EFF;\r\n\t\tmargin-bottom: 10rpx;\r\n\t}\r\n\r\n\t.stat-label {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #666666;\r\n\t}\r\n\r\n\t.signin-tip {\r\n\t\ttext-align: center;\r\n\t\tfont-size: 26rpx;\r\n\t\tcolor: #E6A23C;\r\n\t\tbackground-color: #fdf6ec;\r\n\t\tpadding: 15rpx;\r\n\t\tborder-radius: 8rpx;\r\n\t\tborder-left: 4rpx solid #E6A23C;\r\n\t}\r\n\r\n\t/* 日历样式 */\r\n\t.calendar-container {\r\n\t\tbackground-color: #f8f9fa;\r\n\t\tborder-radius: 12rpx;\r\n\t\tpadding: 20rpx;\r\n\t\tmargin-bottom: 30rpx;\r\n\t}\r\n\r\n\t.calendar-header {\r\n\t\ttext-align: center;\r\n\t\tmargin-bottom: 20rpx;\r\n\t}\r\n\r\n\t.calendar-title {\r\n\t\tfont-size: 32rpx;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #333333;\r\n\t}\r\n\r\n\t.calendar-weekdays {\r\n\t\tdisplay: grid;\r\n\t\tgrid-template-columns: repeat(7, 1fr);\r\n\t\tgap: 10rpx;\r\n\t\tmargin-bottom: 15rpx;\r\n\t\tpadding-bottom: 15rpx;\r\n\t\tborder-bottom: 1rpx solid #e0e0e0;\r\n\t}\r\n\r\n\t.weekday {\r\n\t\ttext-align: center;\r\n\t\tfont-size: 24rpx;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #666666;\r\n\t\tpadding: 10rpx 0;\r\n\t}\r\n\r\n\t.calendar-dates {\r\n\t\tdisplay: grid;\r\n\t\tgrid-template-columns: repeat(7, 1fr);\r\n\t\tgap: 10rpx;\r\n\t}\r\n\r\n\t.calendar-date {\r\n\t\twidth: 60rpx;\r\n\t\theight: 60rpx;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tborder-radius: 50%;\r\n\t\tposition: relative;\r\n\t\tmargin: 0 auto;\r\n\t\tfont-size: 26rpx;\r\n\t\ttransition: all 0.3s ease;\r\n\t}\r\n\r\n\t.calendar-date.empty {\r\n\t\tvisibility: hidden;\r\n\t}\r\n\r\n\t.calendar-date.signed {\r\n\t\tbackground: linear-gradient(135deg, #67C23A 0%, #85ce61 100%);\r\n\t\tcolor: #ffffff;\r\n\t\tbox-shadow: 0 4rpx 12rpx rgba(103, 194, 58, 0.3);\r\n\t}\r\n\r\n\t.calendar-date.today {\r\n\t\tborder: 3rpx solid #409EFF;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #409EFF;\r\n\t}\r\n\r\n\t.calendar-date.disabled {\r\n\t\tcolor: #c0c4cc;\r\n\t}\r\n\r\n\t.signed-mark {\r\n\t\tposition: absolute;\r\n\t\tfont-size: 20rpx;\r\n\t\tfont-weight: bold;\r\n\t}\r\n\r\n\t/* 签到按钮 */\r\n\t.signin-btn {\r\n\t\twidth: 100%;\r\n\t\theight: 80rpx;\r\n\t\tborder-radius: 40rpx;\r\n\t\tfont-size: 32rpx;\r\n\t\tfont-weight: bold;\r\n\t\tmargin-bottom: 30rpx;\r\n\t\tbox-shadow: 0 6rpx 20rpx rgba(64, 158, 255, 0.3);\r\n\t}\r\n\r\n\t/* 签到历史 */\r\n\t.signin-history {\r\n\t\tmargin-top: 20rpx;\r\n\t}\r\n\r\n\t.history-header {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: center;\r\n\t\tmargin-bottom: 20rpx;\r\n\t}\r\n\r\n\t.history-title {\r\n\t\tfont-size: 28rpx;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #333333;\r\n\t}\r\n\r\n\t.history-more {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #409EFF;\r\n\t\tpadding: 10rpx;\r\n\t}\r\n\r\n\t.history-list {\r\n\t\tmax-height: 400rpx;\r\n\t\toverflow-y: auto;\r\n\t}\r\n\r\n\t.history-item {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: center;\r\n\t\tpadding: 20rpx;\r\n\t\tbackground-color: #f8f9fa;\r\n\t\tborder-radius: 8rpx;\r\n\t\tmargin-bottom: 15rpx;\r\n\t\tborder-left: 4rpx solid #409EFF;\r\n\t}\r\n\r\n\t.history-item.extra-reward {\r\n\t\tborder-left-color: #67C23A;\r\n\t\tbackground: linear-gradient(90deg, #f0f9eb 0%, #f8f9fa 100%);\r\n\t}\r\n\r\n\t.history-content {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tgap: 15rpx;\r\n\t}\r\n\r\n\t.history-reward {\r\n\t\tfont-size: 28rpx;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #333333;\r\n\t}\r\n\r\n\t.history-time {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #999999;\r\n\t}\r\n\r\n\t.no-data {\r\n\t\ttext-align: center;\r\n\t\tcolor: #999999;\r\n\t\tpadding: 60rpx 0;\r\n\t\tfont-size: 28rpx;\r\n\t}\r\n\r\n\t/* 无用户提示 */\r\n\t.no-user-tip {\r\n\t\ttext-align: center;\r\n\t\tpadding: 40rpx 20rpx;\r\n\t}\r\n\r\n\t.tip-icon {\r\n\t\tfont-size: 80rpx;\r\n\t\tmargin-bottom: 20rpx;\r\n\t}\r\n\r\n\t.tip-text {\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #666666;\r\n\t\tmargin-bottom: 30rpx;\r\n\t\tline-height: 1.5;\r\n\t}\r\n\r\n\t/* 导航按钮 */\r\n\t.nav-buttons {\r\n\t\tdisplay: flex;\r\n\t\tgap: 20rpx;\r\n\t\tmargin-top: 30rpx;\r\n\t}\r\n\r\n\t.nav-btn {\r\n\t\tflex: 1;\r\n\t\theight: 80rpx;\r\n\t\tborder-radius: 40rpx;\r\n\t\tfont-size: 30rpx;\r\n\t}\r\n\r\n\t/* 响应式设计 */\r\n\t@media screen and (max-width: 750rpx) {\r\n\t\t.signin-stats {\r\n\t\t\tflex-direction: column;\r\n\t\t\tgap: 20rpx;\r\n\t\t}\r\n\t}\r\n\r\n\t@media screen and (max-width: 600rpx) {\r\n\t\t.calendar-dates {\r\n\t\t\tgap: 5rpx;\r\n\t\t}\r\n\r\n\t\t.calendar-date {\r\n\t\t\twidth: 50rpx;\r\n\t\t\theight: 50rpx;\r\n\t\t\tfont-size: 24rpx;\r\n\t\t}\r\n\t}\r\n</style>", "import mod from \"-!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./signin.vue?vue&type=style&index=0&id=23701386&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./signin.vue?vue&type=style&index=0&id=23701386&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755852756332\n      var cssReload = require(\"D:/app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}